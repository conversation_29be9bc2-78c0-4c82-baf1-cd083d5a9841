# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.temp
.cache/

# Secrets and credentials
*.key
*.pem
*.p12
service-account-key.json
.env.local
.env.production
.env.development

# Large files and data
*.zip
*.tar.gz
*.rar
data/
storage/
uploads/

# AnythingLLM specific
anything-llm/
storage/
hotdir/
outputs/

# AI Models (too large for git)
*.bin
*.gguf
models/

# Backup files
*.bak
*.backup

# Test outputs
test_output/
output/
