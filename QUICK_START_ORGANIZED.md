# 🚀 دليل البداية السريعة - البنية المنظمة

## 🎯 مرحباً بك في النظام المنظم!

تم إعادة تنظيم مشروع AI Development Assistant بالكامل لتسهيل الاستخدام والتطوير.

---

## ⚡ البداية السريعة (5 دقائق)

### 1️⃣ تشغيل النظام الأساسي
```bash
# انتقل للمجلد الأساسي
cd 01-core/scripts

# شغل النظام الموحد
./start-unified.ps1
```

### 2️⃣ استخدام الواجهة السريعة
```bash
# انتقل للمساعدين
cd 03-assistants/augment-assistants

# شغل النظام السريع
./START-QUICK-AI.bat

# أو استخدم Python مباشرة
python quick-ai-system.py
```

### 3️⃣ فتح الواجهة الويب
```bash
# افتح في المتصفح
open 03-assistants/augment-assistants/quick-web-interface.html
```

---

## 📁 دليل المجلدات السريع

### 🏗️ للمطورين
```bash
# النظام الأساسي
01-core/
├── docker/     # ملفات Docker
├── configs/    # الإعدادات
└── scripts/    # سكريبتات التشغيل

# الخدمات
02-services/
├── ai-agents/      # الوكلاء الذكيين
├── anything-llm/   # إدارة المعرفة
└── integration-api/ # واجهة التكامل
```

### 🎮 للمستخدمين
```bash
# المساعدين
03-assistants/augment-assistants/
├── quick-ai-system.py        # النظام السريع
├── quick-web-interface.html  # الواجهة الويب
└── START-QUICK-AI.bat       # تشغيل سريع

# التوثيق
05-docs/docs/
├── QUICK_START_GUIDE.md     # دليل البداية
└── SYSTEM_STATUS_REPORT.md  # تقرير الحالة
```

### 📊 للباحثين
```bash
# التقارير
08-reports/
├── BIG_PROJECT_ANALYSIS_REPORT.json  # تحليل المشروع
└── FINAL_REORGANIZATION_REPORT.md    # تقرير التنظيم

# الأدوات
09-tools/
├── project-analysis-assistant.py     # مساعد التحليل
└── augment-assistants-interface.py   # واجهة المساعدين
```

---

## 🎯 الخدمات والمنافذ

| الخدمة | المنفذ | الرابط | الاستخدام |
|--------|--------|---------|-----------|
| 🎯 AI Coordinator | 4003 | http://localhost:4003 | تنسيق الذكاء الاصطناعي |
| 🤖 Ollama | 11434 | http://localhost:11434 | النماذج المحلية |
| 📚 AnythingLLM | 4001 | http://localhost:4001 | إدارة المعرفة |
| ⚡ n8n | 4002 | http://localhost:4002 | أتمتة العمليات |

---

## 🛠️ أوامر مفيدة

### 🐳 Docker
```bash
# عرض الحاويات النشطة
docker ps

# تشغيل خدمة معينة
docker start ai-coordinator
docker start ollama
docker start anythingllm
docker start n8n

# إيقاف جميع الخدمات
docker stop $(docker ps -q)
```

### 🤖 الوكلاء الذكيين
```bash
cd 02-services/ai-agents

# تشغيل وكيل الذاكرة (يعمل ✅)
python memory-agent.py

# تشغيل وكيل البحث
python file-search-agent.py

# تشغيل وكيل النظام
python terminal-agent.py
```

### 🎮 المساعدين السريعين
```bash
cd 03-assistants/augment-assistants

# النظام التفاعلي
python quick-ai-system.py

# تشغيل سريع شامل
./START-QUICK-AI.bat
```

---

## 🔧 حل المشاكل الشائعة

### ❌ الخدمات لا تعمل
```bash
# تشخيص النظام
cd 01-core/scripts
./diagnose-system.ps1

# إصلاح الخدمات
./fix-services.ps1
```

### ❌ المنافذ مشغولة
```bash
# فحص المنافذ
netstat -an | findstr :4001
netstat -an | findstr :4003
netstat -an | findstr :11434

# إيقاف العمليات
taskkill /f /im docker.exe
```

### ❌ مشاكل Python
```bash
# تفعيل البيئة الافتراضية
.venv\Scripts\activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

---

## 📚 موارد إضافية

### 📖 التوثيق
- **دليل شامل**: `05-docs/docs/QUICK_START_GUIDE.md`
- **هيكل المشروع**: `PROJECT_STRUCTURE_ORGANIZED.md`
- **تقرير التنظيم**: `08-reports/FINAL_REORGANIZATION_REPORT.md`

### 🛠️ أدوات التطوير
- **مساعد التحليل**: `09-tools/project-analysis-assistant.py`
- **واجهة المساعدين**: `09-tools/augment-assistants-interface.py`
- **اختبار النظام**: `06-tests/tests/test_ai_system.ps1`

### 🎯 أمثلة عملية
- **محادثة سريعة**: استخدم الواجهة الويب
- **تحليل مشروع**: استخدم أدوات التحليل
- **أتمتة مهام**: استخدم n8n workflows

---

## 🎉 نصائح للنجاح

### ✅ للمبتدئين
1. ابدأ بالواجهة السريعة
2. استخدم `START-QUICK-AI.bat`
3. اقرأ `QUICK_START_GUIDE.md`

### ✅ للمطورين
1. استخدم البنية المنظمة
2. اتبع فصل الاهتمامات
3. اختبر التغييرات في `06-tests/`

### ✅ للمتقدمين
1. طور في `02-services/`
2. استخدم `04-memory/` للذاكرة
3. أنشئ workflows في `07-workflows/`

---

<div align="center">
  <strong>🚀 مشروع منظم وجاهز للاستخدام!</strong>
  <br>
  <em>استمتع بالتطوير الذكي المنظم</em>
</div>
