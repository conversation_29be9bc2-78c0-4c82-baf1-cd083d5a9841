# 🧪 اختبار الخدمات الأساسية
Write-Host "🔍 اختبار الخدمات الأساسية..." -ForegroundColor Green

# قائمة الخدمات للاختبار
$services = @(
    @{Name="AnythingLLM"; URL="http://localhost:4001"; Port=4001},
    @{Name="n8n"; URL="http://localhost:4002"; Port=4002},
    @{Name="AI Coordinator"; URL="http://localhost:4003"; Port=4003},
    @{Name="Ollama WebUI"; URL="http://localhost:4000"; Port=4000},
    @{Name="Ollama API"; URL="http://localhost:11434"; Port=11434}
)

Write-Host "`n📊 فحص المنافذ..." -ForegroundColor Yellow

foreach ($service in $services) {
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $service.Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✅ $($service.Name) (المنفذ $($service.Port)): متاح" -ForegroundColor Green
        } else {
            Write-Host "❌ $($service.Name) (المنفذ $($service.Port)): غير متاح" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ $($service.Name): خطأ في الاختبار" -ForegroundColor Red
    }
}

Write-Host "`n🌐 اختبار HTTP Response..." -ForegroundColor Yellow

foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.URL -Method GET -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name): يستجيب بنجاح (200)" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $($service.Name): يستجيب بكود $($response.StatusCode)" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "❌ $($service.Name): لا يستجيب - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🦙 اختبار Ollama..." -ForegroundColor Yellow
try {
    $models = ollama list 2>$null
    if ($models) {
        Write-Host "✅ Ollama: يعمل ولديه نماذج" -ForegroundColor Green
        Write-Host "📋 النماذج المتاحة:" -ForegroundColor Cyan
        ollama list
    } else {
        Write-Host "❌ Ollama: لا توجد نماذج" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Ollama: غير متاح" -ForegroundColor Red
}

Write-Host "`n🎉 انتهى اختبار الخدمات الأساسية!" -ForegroundColor Green
