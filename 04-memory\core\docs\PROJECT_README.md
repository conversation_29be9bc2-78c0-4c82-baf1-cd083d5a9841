# 🤖 AI Development Assistant - Integrated System

[![GitHub Stars](https://img.shields.io/github/stars/amrashour2/ai-development-assistant?style=social)](https://github.com/amrashour2/ai-development-assistant)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![Python](https://img.shields.io/badge/Python-3.8+-green.svg)](https://www.python.org/)

## نظرة عامة
نظام متكامل للذكاء الاصطناعي يجمع بين AnythingLLM و Ollama و n8n لإنشاء بيئة تطوير ذكية ومتقدمة.

## المكونات الرئيسية

### 🤖 AnythingLLM
- مركز إدارة النماذج والمحادثات
- واجهة ويب متقدمة للتفاعل مع النماذج
- دعم متعدد النماذج والمصادر

### 🧠 Ollama
- تشغيل النماذج المحلية
- النماذج المتاحة:
  - `llama3:8b` - للمهام المعقدة والبرمجة
  - `gemma3n:e4b` - نموذج Google المتقدم
  - `mistral:7b` - للمهام السريعة
  - `phi3:mini` - للمهام البسيطة

### 🔄 n8n Automation
- أتمتة سير العمل
- AI Dev Assistant Workflow
- تكامل مع APIs مختلفة

## الميزات

### 🛠️ AI Dev Assistant Workflow
سير عمل ذكي لمساعدة المطورين:
1. **تحليل المتطلبات** - باستخدام Google Gemini
2. **كتابة الكود** - باستخدام Ollama
3. **حفظ النتائج** - في ملفات منظمة

### 📊 AI Dashboard
لوحة تحكم لمراقبة النظام:
- حالة الخدمات
- إحصائيات الاستخدام
- إدارة النماذج

### 🔧 VSCode AI Controller
تحكم في النماذج من داخل VSCode:
- تشغيل وإيقاف النماذج
- مراقبة الأداء
- تبديل بين النماذج

## التثبيت والتشغيل

### المتطلبات
- Docker & Docker Compose
- Python 3.8+
- Git

### التثبيت السريع
```bash
# استنساخ المشروع
git clone <repository-url>
cd anything-llm

# تشغيل النظام
docker-compose up -d

# تثبيت المتطلبات Python
pip install -r requirements.txt
```

### الوصول للخدمات
- **AnythingLLM**: http://localhost:3001
- **n8n**: http://localhost:5678
- **Ollama API**: http://localhost:11434
- **AI Dashboard**: python ai_dashboard/app.py

## الاستخدام

### AI Dev Assistant Workflow
```bash
# اختبار الـ workflow
python test_ai_workflow.py

# أو استخدام curl
curl -X POST http://localhost:5678/webhook/a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22 \
  -H "Content-Type: application/json" \
  -d '{
    "feature_request": "نظام تسجيل دخول مع JWT",
    "feature_name": "auth_system"
  }'
```

### VSCode Integration
```python
# تشغيل المتحكم
python vscode_ai_controller.py
```

## الملفات المهمة

### التكوين
- `docker-compose.yml` - إعدادات Docker
- `requirements.txt` - متطلبات Python
- `.gitignore` - ملفات مستبعدة من Git

### Workflows
- `ai_dev_assistant_workflow.json` - n8n workflow
- `import_workflow_guide.md` - دليل الاستيراد

### Scripts
- `test_ai_workflow.py` - اختبار الـ workflow
- `vscode_ai_controller.py` - تحكم VSCode
- `install_dependencies.py` - تثبيت المتطلبات

### Documentation
- `README_AI_WORKFLOW.md` - دليل الـ workflow
- `system_integration_guide.md` - دليل التكامل
- `memory/` - ذاكرة النظام والإعدادات

## التطوير

### إضافة نماذج جديدة
```bash
# تحميل نموذج جديد
ollama pull model_name

# تحديث الـ workflow في n8n
```

### تخصيص الـ Workflows
1. افتح n8n على http://localhost:5678
2. عدّل الـ workflow حسب احتياجاتك
3. احفظ التغييرات

## استكشاف الأخطاء

### مشاكل شائعة
- **Ollama لا يعمل**: تحقق من `ollama serve`
- **n8n لا يتصل**: تحقق من Docker network
- **النماذج لا تظهر**: أعد تشغيل Ollama

### Logs
```bash
# logs Docker
docker logs anythingllm
docker logs n8n_automation

# logs Ollama
ollama logs
```

## المساهمة
1. Fork المشروع
2. أنشئ branch جديد
3. اعمل التغييرات
4. أرسل Pull Request

## الترخيص
MIT License

## الدعم
- راجع الـ documentation في مجلد `memory/`
- تحقق من الـ issues في GitHub
- استخدم الـ AI Assistant للمساعدة

---
تم تطوير هذا النظام لتوفير بيئة تطوير ذكية ومتكاملة باستخدام أحدث تقنيات الذكاء الاصطناعي.
