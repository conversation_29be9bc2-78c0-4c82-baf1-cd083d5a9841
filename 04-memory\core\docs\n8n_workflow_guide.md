# دليل إنشاء AI Dev Assistant Workflow في n8n

## الخطوة 1: الوصول إلى n8n
1. افتح المتصفح واذهب إلى: `http://localhost:5678`
2. سجل الدخول أو أنشئ حساب جديد

## الخطوة 2: إنشاء Workflow جديد
1. انقر على "New Workflow" أو "إنشاء سير عمل جديد"
2. اسم الـ workflow: **AI Dev Assistant - Economic Workflow**

## الخطوة 3: إضافة العقد (Nodes)

### العقدة 1: Start Node
- **النوع**: Start
- **الموقع**: (240, 300)
- **الإعدادات**: افتراضية

### العقدة 2: Webhook
- **النوع**: Webhook
- **الموقع**: (460, 300)
- **الإعدادات**:
  - Path: `a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22`
  - HTTP Method: POST
  - Response Mode: On Received

### العقدة 3: Google Gemini
- **النوع**: Google Gemini
- **الموقع**: (680, 300)
- **الإعدادات**:
  - Model: `gemini-pro`
  - Prompt: 
    ```
    أنا مطور برامج وأحتاج إلى بناء الميزة التالية:

    {{ $json.body.feature_request }}

    مهمتك: قم بالبحث في الإنترنت عن أفضل الممارسات لبناء هذه الميزة، ثم قدم لي خطة تنفيذ مفصلة ومقسمة إلى خطوات واضحة للمطورين.
    ```
- **Credentials**: ستحتاج لإعداد Google Gemini API credentials

### العقدة 4: Ollama
- **النوع**: Ollama
- **الموقع**: (900, 300)
- **الإعدادات**:
  - Base URL: `http://**********:11434`
  - Model: `llama3:8b`
  - Prompt:
    ```
    بناءً على الخطة التالية التي وضعها الخبير:

    {{ $('Google Gemini').json.response }}

    مهمتك: اكتب مسودة أولية للكود (HTML, CSS, JS, أو Python حسب الحاجة) لتنفيذ هذه الخطة. ركز على بناء هيكل الكود بشكل واضح.
    ```

### العقدة 5: Write Binary File
- **النوع**: Write Binary File
- **الموقع**: (1120, 300)
- **الإعدادات**:
  - File Name: `={{ '/data/output/' + ($json.body.feature_name || 'feature') + '.md' }}`
  - Data: `={{ $('Ollama').json.response }}`

## الخطوة 4: ربط العقد
1. **Webhook** → **Google Gemini**
2. **Google Gemini** → **Ollama**
3. **Ollama** → **Write Binary File**

## الخطوة 5: إعداد Credentials

### Google Gemini API:
1. اذهب إلى Settings → Credentials
2. أضف credential جديد من نوع "Google Gemini API"
3. أدخل API Key الخاص بك

## الخطوة 6: اختبار الـ Workflow

### طريقة الاختبار:
1. احفظ الـ workflow
2. فعّل الـ workflow (Active)
3. استخدم webhook URL لإرسال طلب POST:

```bash
curl -X POST http://localhost:5678/webhook/a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22 \
  -H "Content-Type: application/json" \
  -d '{
    "feature_request": "نظام تسجيل دخول للمستخدمين مع التحقق من الهوية",
    "feature_name": "user_authentication"
  }'
```

## الخطوة 7: مراقبة النتائج
- تحقق من executions في n8n
- ابحث عن الملف المُنشأ في `/data/output/`

## ملاحظات مهمة:
- تأكد من أن Ollama يعمل على `http://**********:11434`
- تأكد من وجود مجلد `/data/output/` أو قم بإنشائه
- قم بإعداد Google Gemini API key بشكل صحيح

## استكشاف الأخطاء:
1. **خطأ في Ollama**: تحقق من أن العنوان صحيح وأن النموذج متاح
2. **خطأ في Gemini**: تحقق من صحة API key
3. **خطأ في الكتابة**: تحقق من أذونات المجلد
