# 🚀 دليل التشغيل السريع - نظام AI المتكامل

## ✅ الحالة الحالية
جميع الخدمات تعمل بنجاح:

- **AnythingLLM**: http://localhost:4001 (مركز القيادة)
- **n8n**: http://localhost:4002 (مدير الأتمتة) - المستخدم: admin، كلمة المرور: password123
- **AI Coordinator**: http://localhost:4003 (طبقة التنسيق الذكية)
- **Ollama WebUI**: http://localhost:4000 (واجهة النماذج المحلية)
- **Ollama API**: http://localhost:11434 (النماذج المحلية)

## 🔧 الخطوة التالية: استيراد Workflow إلى n8n

### 1. الدخول إلى n8n:
- افتح http://localhost:4002
- المستخدم: `admin`
- كلمة المرور: `password123`

### 2. استيراد Workflow الجاهز:
1. انقر على "Add workflow" أو "+"
2. انقر على الثلاث نقاط (⋯) في الأعلى
3. اختر "Import from file" أو "Import"
4. اختر الملف: `model_mix/ai-coordinator/n8n-workflow.json`
5. انقر "Import"

### 3. تفعيل Workflow:
1. بعد الاستيراد، انقر على "Activate" في الأعلى
2. ستحصل على webhook URL مثل: `http://localhost:4002/webhook/ai-coordinator`

## 🧪 اختبار النظام

### اختبار AI Coordinator:
```bash
# في PowerShell
Invoke-RestMethod -Uri "http://localhost:4003/api/coordinate" -Method POST -ContentType "application/json" -Body '{"prompt": "مرحبا، كيف حالك؟", "options": {"priority": "fast"}}'
```

### اختبار Ollama:
```bash
# في PowerShell
Invoke-RestMethod -Uri "http://localhost:11434/api/generate" -Method POST -ContentType "application/json" -Body '{"model": "llama3:8b", "prompt": "مرحبا", "stream": false}'
```

## 🎯 الخطوات التالية

1. **إعداد AnythingLLM**: ربطه بـ Ollama والـ AI Coordinator
2. **إنشاء VS Code Scripts**: للتفاعل مع النظام من المحرر
3. **اختبار التكامل الكامل**: تجربة سيناريوهات مختلفة
4. **التحضير للنشر السحابي**: إعداد Google Cloud

## 🔗 الروابط المهمة

- **AnythingLLM**: http://localhost:4001
- **n8n Dashboard**: http://localhost:4002
- **AI Coordinator API**: http://localhost:4003/api/health
- **Ollama WebUI**: http://localhost:4000

## 📝 ملاحظات

- جميع النماذج المحلية جاهزة: llama3:8b, gemma3n:e4b, mistral:7b, phi3:mini
- مفاتيح Gemini API محدثة ومكونة
- الشبكة الموحدة (ai-dev-network) تعمل بنجاح
- AI Coordinator يدعم التنسيق الذكي بين النماذج

---

**🎉 النظام جاهز للاستخدام! ابدأ بتجربة الـ workflows في n8n.**
