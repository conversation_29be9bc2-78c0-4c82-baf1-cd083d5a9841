#!/usr/bin/env python3
"""
فحص المشروع الكبير باستخدام المساعدين المعزولين
Project Analysis Using Isolated Assistants
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

# استيراد نظام المساعدين
sys.path.append('.')
from start_using_assistants import AssistantsManager

class ProjectAnalyzer:
    """محلل المشروع باستخدام المساعدين"""
    
    def __init__(self):
        self.assistants = AssistantsManager()
        self.project_root = Path("..").resolve()
        self.analysis_results = {}
        
    def analyze_project_structure(self):
        """تحليل هيكل المشروع"""
        print("🔍 تحليل هيكل المشروع باستخدام المساعدين...")
        
        # استشارة Gemini حول هيكل المشروع
        structure_query = f"""
        أحتاج تحليل هيكل مشروع AI Development Assistant الذي يحتوي على:
        - anything-llm (نظام إدارة المعرفة)
        - ai-agents (وكلاء ذكيين)
        - ai-integration-system (نظام التكامل)
        - model_mix (تكامل النماذج)
        - memory (نظام الذاكرة)
        - augment-assistants (المساعدين المعزولين)
        
        ما أفضل طريقة لتنظيم وتطوير هذا المشروع المعقد؟
        """
        
        gemini_response = self.assistants.consult_assistant(structure_query, "gemini")
        
        # حفظ النتيجة
        self.analysis_results["structure_analysis"] = {
            "query": structure_query,
            "response": gemini_response,
            "timestamp": datetime.now().isoformat()
        }
        
        # إضافة رؤية للمشروع
        self.assistants.add_project_insight(
            "structure_analysis", 
            f"تم تحليل هيكل المشروع: {gemini_response[:100]}..."
        )
        
        print(f"✅ تحليل الهيكل مكتمل")
        return gemini_response
    
    def analyze_containers_and_services(self):
        """تحليل الحاويات والخدمات"""
        print("🐳 تحليل الحاويات والخدمات...")
        
        containers_query = """
        لدي مشروع يحتوي على 22 حاوية Docker مختلفة تشمل:
        - AnythingLLM (Port 4001)
        - n8n (Port 5678) 
        - AI Coordinator (Port 3333)
        - Ollama (Port 11434)
        - MCP Servers متعددة
        - قواعد بيانات (PostgreSQL, Redis)
        
        كيف يمكنني توحيد المنافذ وتحسين التكامل بين هذه الخدمات؟
        """
        
        containers_response = self.assistants.consult_assistant(containers_query, "gemini")
        
        self.analysis_results["containers_analysis"] = {
            "query": containers_query,
            "response": containers_response,
            "timestamp": datetime.now().isoformat()
        }
        
        self.assistants.add_project_insight(
            "containers_optimization",
            f"تحليل الحاويات: {containers_response[:100]}..."
        )
        
        print(f"✅ تحليل الحاويات مكتمل")
        return containers_response
    
    def analyze_ai_integration(self):
        """تحليل تكامل الذكاء الاصطناعي"""
        print("🧠 تحليل تكامل الذكاء الاصطناعي...")
        
        ai_query = """
        مشروعي يحتوي على:
        - Ollama مع 4 نماذج محلية (llama3:8b, gemma3n:e4b, codellama:7b, mistral:7b)
        - Gemini API للمهام المعقدة
        - AI Coordinator للتنسيق بين النماذج
        - AI Agents متخصصين (memory, file-search, terminal, data-analysis)
        
        كيف يمكنني تحسين التعاون بين هذه النماذج والوكلاء؟
        """
        
        ai_response = self.assistants.consult_assistant(ai_query, "gemini")
        
        self.analysis_results["ai_integration"] = {
            "query": ai_query,
            "response": ai_response,
            "timestamp": datetime.now().isoformat()
        }
        
        self.assistants.add_project_insight(
            "ai_optimization",
            f"تحسين التكامل: {ai_response[:100]}..."
        )
        
        print(f"✅ تحليل التكامل مكتمل")
        return ai_response
    
    def identify_development_priorities(self):
        """تحديد أولويات التطوير"""
        print("🎯 تحديد أولويات التطوير...")
        
        priorities_query = """
        بناءً على تحليل مشروع AI Development Assistant المعقد، ما هي أهم 5 أولويات للتطوير؟
        
        المشاكل الحالية:
        - بعض AI Agents تحتاج إصلاح
        - المنافذ غير موحدة
        - الخدمات متوقفة حالياً
        - Gemini CLI يحتاج إعداد
        
        النقاط القوية:
        - AI Coordinator يعمل بشكل ممتاز
        - Ollama متكامل مع 4 نماذج
        - نظام ذاكرة متطور
        - توثيق شامل
        """
        
        priorities_response = self.assistants.consult_assistant(priorities_query, "gemini")
        
        self.analysis_results["development_priorities"] = {
            "query": priorities_query,
            "response": priorities_response,
            "timestamp": datetime.now().isoformat()
        }
        
        self.assistants.add_project_insight(
            "development_roadmap",
            f"أولويات التطوير: {priorities_response[:100]}..."
        )
        
        print(f"✅ تحديد الأولويات مكتمل")
        return priorities_response
    
    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        print("📊 إنشاء تقرير شامل...")
        
        # جمع حالة المشروع من المساعدين
        project_status = self.assistants.get_project_status()
        
        # إنشاء التقرير
        report = {
            "analysis_timestamp": datetime.now().isoformat(),
            "project_status": project_status,
            "analysis_results": self.analysis_results,
            "recommendations": {
                "immediate_actions": [
                    "إصلاح AI Agents المتبقية",
                    "توحيد المنافذ باستخدام docker-compose-unified.yml",
                    "تشغيل النظام الموحد",
                    "إعداد Gemini CLI بشكل صحيح"
                ],
                "short_term_goals": [
                    "تطوير Integration API",
                    "تحسين التواصل بين الخدمات",
                    "إنشاء نظام مراقبة",
                    "تطوير Frontend Gateway"
                ],
                "long_term_vision": [
                    "نظام AI متكامل بالكامل",
                    "أتمتة متقدمة للمهام",
                    "واجهة موحدة للمستخدمين",
                    "نظام تعلم تكيفي"
                ]
            },
            "assistant_insights": "تم استخدام المساعدين المعزولين بنجاح لتحليل المشروع"
        }
        
        # حفظ التقرير
        report_file = Path("../PROJECT_ANALYSIS_REPORT.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ التقرير في: {report_file}")
        return report
    
    def run_full_analysis(self):
        """تشغيل التحليل الكامل"""
        print("🚀 بدء التحليل الشامل للمشروع الكبير باستخدام المساعدين")
        print("=" * 70)
        
        # تسجيل بدء التحليل
        self.assistants.log_activity(
            "بدء التحليل الشامل للمشروع الكبير",
            "استخدام جميع المساعدين المعزولين"
        )
        
        # تشغيل التحليلات
        structure_result = self.analyze_project_structure()
        containers_result = self.analyze_containers_and_services()
        ai_result = self.analyze_ai_integration()
        priorities_result = self.identify_development_priorities()
        
        # إنشاء التقرير النهائي
        final_report = self.generate_comprehensive_report()
        
        print("\n🎉 التحليل الشامل مكتمل!")
        print("📋 النتائج:")
        print(f"  - تحليل الهيكل: ✅")
        print(f"  - تحليل الحاويات: ✅")
        print(f"  - تحليل التكامل: ✅")
        print(f"  - تحديد الأولويات: ✅")
        print(f"  - التقرير النهائي: ✅")
        
        return final_report

def main():
    """الدالة الرئيسية"""
    analyzer = ProjectAnalyzer()
    report = analyzer.run_full_analysis()
    
    print("\n💡 ملخص التوصيات:")
    for category, actions in report["recommendations"].items():
        print(f"\n{category}:")
        for action in actions:
            print(f"  • {action}")
    
    return analyzer

if __name__ == "__main__":
    project_analyzer = main()
