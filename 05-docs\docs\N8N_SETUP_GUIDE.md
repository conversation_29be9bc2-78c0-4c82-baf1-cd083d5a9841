# ⚙️ دليل إعداد n8n

## 🔐 الدخول إلى n8n

### معلومات الدخول:
- **الرابط**: http://localhost:4002
- **المستخدم**: `admin`
- **كلمة المرور**: `password123`

## 📥 استيراد Workflow الجاهز

### الخطوة 1: الدخول إلى n8n
1. افتح http://localhost:4002
2. أدخل المستخدم وكلمة المرور
3. ستصل إلى لوحة التحكم

### الخطوة 2: استيراد Workflow
1. انقر على **"Add workflow"** أو الزر **"+"**
2. انقر على الثلاث نقاط **"⋯"** في الأعلى
3. اختر **"Import from file"** أو **"Import"**
4. ا<PERSON><PERSON><PERSON> الملف: `model_mix/ai-coordinator/n8n-workflow.json`
5. انقر **"Import"**

### الخطوة 3: تفعيل Workflow
1. بعد الاستيراد، انقر على **"Activate"** في الأعلى
2. ستحصل على webhook URL مثل: `http://localhost:4002/webhook/ai-coordinator`

## 🧪 اختبار Workflow

### اختبار بسيط:
1. انقر على **"Execute Workflow"** أو **"Test workflow"**
2. أدخل بيانات اختبار
3. راقب النتائج في كل خطوة

### اختبار عبر Webhook:
```bash
# في PowerShell
$body = @{
    prompt = "مرحبا من n8n"
    options = @{
        priority = "fast"
        complexity = "low"
    }
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:4002/webhook/ai-coordinator" -Method POST -ContentType "application/json" -Body $body
```

## 🔧 إعداد Connections

### ربط n8n مع AI Coordinator:
1. في Workflow، انقر على عقدة **HTTP Request**
2. أدخل URL: `http://ai-coordinator:3333/api/coordinate`
3. اختر Method: **POST**
4. أضف Headers: `Content-Type: application/json`

### ربط n8n مع Ollama:
1. أضف عقدة **HTTP Request** جديدة
2. أدخل URL: `http://host.docker.internal:11434/api/generate`
3. اختر Method: **POST**
4. أضف Body مع بيانات النموذج

## 📊 مراقبة Workflows

### عرض Executions:
1. انقر على **"Executions"** في القائمة الجانبية
2. ستجد قائمة بجميع التشغيلات
3. انقر على أي تشغيل لرؤية التفاصيل

### تتبع الأخطاء:
1. التشغيلات الفاشلة ستظهر باللون الأحمر
2. انقر عليها لرؤية رسالة الخطأ
3. استخدم هذه المعلومات لإصلاح المشاكل

## 🎯 Workflows مقترحة للاختبار

### Workflow 1: اختبار بسيط
```json
{
  "nodes": [
    {
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook"
    },
    {
      "name": "AI Coordinator",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "http://ai-coordinator:3333/api/coordinate",
        "method": "POST"
      }
    }
  ]
}
```

### Workflow 2: تعاون النماذج
1. **Webhook** - استقبال الطلب
2. **Decision Node** - تحديد النموذج المناسب
3. **Ollama Node** - للمهام السريعة
4. **Gemini Node** - للمهام المعقدة
5. **Response Node** - إرجاع النتيجة

## ❌ استكشاف الأخطاء

### مشكلة: لا يمكن الوصول لـ n8n
- تحقق من أن Container يعمل: `docker ps | grep n8n`
- أعد تشغيل n8n: `docker-compose restart n8n`
- تحقق من logs: `docker-compose logs n8n`

### مشكلة: Webhook لا يعمل
- تأكد من أن Workflow مفعل (Active)
- تحقق من URL الصحيح
- جرب Test execution أولاً

### مشكلة: لا يتصل بالخدمات الأخرى
- تأكد من أن جميع الخدمات على نفس الشبكة
- استخدم أسماء الخدمات بدلاً من localhost
- مثال: `http://ai-coordinator:3333` بدلاً من `http://localhost:4003`

## 🎉 النجاح!

إذا تمكنت من:
- ✅ الدخول إلى n8n
- ✅ استيراد workflow
- ✅ تفعيل workflow
- ✅ اختبار execution

فأنت جاهز للخطوة التالية!

---

**💡 نصيحة**: احفظ workflows المفيدة وأنشئ نسخ احتياطية منها.
