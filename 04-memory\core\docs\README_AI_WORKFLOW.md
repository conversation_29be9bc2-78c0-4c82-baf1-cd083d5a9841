# AI Dev Assistant Workflow - دليل التشغيل الكامل

## نظرة عامة
هذا الـ workflow يستخدم الذكاء الاصطناعي لمساعدة المطورين في بناء الميزات البرمجية:
1. **Google Gemini**: يح<PERSON><PERSON> المتطلبات ويضع خطة تنفيذ
2. **Ollama (Llama3)**: يكتب الكود بناءً على الخطة
3. **حفظ النتيجة**: في ملف Markdown

## الملفات المُنشأة:
- `ai_dev_assistant_workflow.json` - ملف الـ workflow للاستيراد
- `import_workflow_guide.md` - دليل الاستيراد التفصيلي
- `test_ai_workflow.py` - سكريبت اختبار الـ workflow

## خطوات التشغيل السريع:

### 1. استيراد الـ Workflow
```bash
# افتح n8n في المتصفح
http://localhost:5678

# سجل الدخول:
# Email: <EMAIL>
# Password: 2452329511@Amr

# استورد الملف: ai_dev_assistant_workflow.json
```

### 2. إعداد Google Gemini API
- أضف credential جديد من نوع "Google Gemini API"
- أدخل API Key الخاص بك

### 3. تفعيل الـ Workflow
- احفظ الـ workflow
- فعّل الـ workflow (زر Active)

### 4. اختبار الـ Workflow
```bash
python test_ai_workflow.py
```

## مثال على الاستخدام:

### إرسال طلب عبر curl:
```bash
curl -X POST http://localhost:5678/webhook/a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22 \
  -H "Content-Type: application/json" \
  -d '{
    "feature_request": "نظام إدارة المهام مع إمكانية إضافة وحذف وتعديل المهام",
    "feature_name": "task_management_system"
  }'
```

### إرسال طلب عبر Python:
```python
import requests

url = "http://localhost:5678/webhook/a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22"
data = {
    "feature_request": "صفحة عرض المنتجات مع فلترة وبحث",
    "feature_name": "product_catalog"
}

response = requests.post(url, json=data)
print(response.status_code)
```

## عرض النتائج:
```bash
# عرض الملفات المُنشأة
docker exec n8n_automation ls -la /data/output/

# عرض محتوى ملف معين
docker exec n8n_automation cat /data/output/task_management_system.md
```

## حالة النظام:
✅ **n8n**: يعمل على المنفذ 5678  
✅ **Ollama**: يعمل محلياً على المنفذ 11434  
✅ **النماذج المتاحة**: llama3:8b, gemma3n:e4b, mistral:7b, phi3:mini  
✅ **مجلد الإخراج**: تم إنشاؤه في `/data/output/`  

## استكشاف الأخطاء:

### إذا لم يعمل Ollama:
```bash
# تحقق من حالة Ollama
ollama list

# تأكد من أن النموذج متاح
ollama run llama3:8b
```

### إذا لم تظهر النتائج:
```bash
# تحقق من executions في n8n
# أو تحقق من logs الحاوية
docker logs n8n_automation
```

## الخطوات التالية:
1. جرب الـ workflow مع طلبات مختلفة
2. عدّل الـ prompts حسب احتياجاتك
3. أضف عقد جديدة للمعالجة الإضافية
4. ادمج مع أنظمة أخرى (GitHub, Slack, etc.)

## دعم:
- تحقق من الدليل التفصيلي: `import_workflow_guide.md`
- استخدم سكريبت الاختبار: `test_ai_workflow.py`
- راجع logs n8n للتشخيص
