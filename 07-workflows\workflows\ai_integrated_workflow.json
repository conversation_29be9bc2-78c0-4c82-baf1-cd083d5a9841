{"name": "AI Integrated Development Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ai-dev-assistant", "responseMode": "responseNode", "options": {}}, "id": "webhook-start", "name": "Webhook Start", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "ai-dev-assistant-webhook"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.body.complexity || 'medium'}}", "operation": "equal", "value2": "high"}]}}, "id": "complexity-decision", "name": "Complexity Decision", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "contents", "value": "=[{\"parts\":[{\"text\":\"{{$json.body.prompt || 'مرحبا'}}\"}]}]"}]}, "options": {}}, "id": "gemini-strategic", "name": "Gemini Strategic Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 200], "credentials": {"httpHeaderAuth": {"id": "gemini-api-key", "name": "Gemini API Key"}}}, {"parameters": {"url": "http://ai-coordinator:3333/api/coordinate", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{$json.body.prompt}}"}, {"name": "options", "value": "={\"priority\": \"normal\", \"complexity\": \"{{$json.body.complexity || 'medium'}}\"}"}]}, "options": {}}, "id": "ai-coordinator", "name": "AI Coordinator", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 400]}, {"parameters": {"url": "http://host.docker.internal:11434/api/generate", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama3:8b"}, {"name": "prompt", "value": "بناءً على التحليل الاستراتيجي التالي:\n\n{{$('Gemini Strategic Analysis').json.candidates[0].content.parts[0].text}}\n\nالآن قم بتنفيذ المهمة: {{$json.body.prompt}}"}, {"name": "stream", "value": false}]}, "options": {}}, "id": "ollama-execution", "name": "Ollama Execution", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"url": "http://anythingllm:3001/api/v1/workspace/ai-development/chat", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer {{$env.AUTH_TOKEN}}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "message", "value": "حفظ النتيجة التالية في الذاكرة:\n\nالطلب: {{$json.body.prompt}}\nالتحليل الاستراتيجي: {{$('Gemini Strategic Analysis').json.candidates[0].content.parts[0].text}}\nالتنفيذ: {{$('Ollama Execution').json.response}}"}, {"name": "mode", "value": "chat"}]}, "options": {}}, "id": "anythingllm-memory", "name": "AnythingLLM Memory", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"timestamp\": \"{{new Date().toISOString()}}\",\n  \"request\": {\n    \"prompt\": \"{{$json.body.prompt}}\",\n    \"complexity\": \"{{$json.body.complexity || 'medium'}}\"\n  },\n  \"strategic_analysis\": \"{{$('Gemini Strategic Analysis').json.candidates[0].content.parts[0].text}}\",\n  \"execution_result\": \"{{$('Ollama Execution').json.response}}\",\n  \"coordinator_response\": \"{{$('AI Coordinator').json.response}}\",\n  \"memory_saved\": true,\n  \"workflow\": \"AI Integrated Development\"\n}"}, "id": "final-response", "name": "Final Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}], "connections": {"Webhook Start": {"main": [[{"node": "Complexity Decision", "type": "main", "index": 0}]]}, "Complexity Decision": {"main": [[{"node": "Gemini Strategic Analysis", "type": "main", "index": 0}], [{"node": "AI Coordinator", "type": "main", "index": 0}]]}, "Gemini Strategic Analysis": {"main": [[{"node": "Ollama Execution", "type": "main", "index": 0}]]}, "AI Coordinator": {"main": [[{"node": "AnythingLLM Memory", "type": "main", "index": 0}]]}, "Ollama Execution": {"main": [[{"node": "AnythingLLM Memory", "type": "main", "index": 0}]]}, "AnythingLLM Memory": {"main": [[{"node": "Final Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1.0", "meta": {"instanceId": "ai-integrated-development-workflow"}}