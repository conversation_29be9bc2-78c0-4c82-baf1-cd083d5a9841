# سكريبت تنظيف العمليات في الخلفية
# =====================================

param(
    [switch]$Force,
    [switch]$DryRun,
    [switch]$KeepVSCode
)

Write-Host "🧹 تنظيف العمليات في الخلفية..." -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# الحصول على معرف عملية VS Code الحالية
$currentVSCodePID = $null
if ($env:VSCODE_PID) {
    $currentVSCodePID = $env:VSCODE_PID
    Write-Host "🔒 VS Code الحالي محمي: PID $currentVSCodePID" -ForegroundColor Green
}

# 1. إغلاق عمليات Node.js غير الضرورية
Write-Host "`n🟡 فحص عمليات Node.js..." -ForegroundColor Yellow

$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
$criticalNodeProcesses = @()
$safeToKillNodeProcesses = @()

foreach ($proc in $nodeProcesses) {
    try {
        $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($proc.Id)").CommandLine
        
        # العمليات المهمة التي يجب الاحتفاظ بها
        if ($commandLine -match "anythingllm|ollama|n8n|ai-coordinator") {
            $criticalNodeProcesses += $proc
            Write-Host "   🔒 محمي: $($proc.Id) - $($commandLine.Substring(0, [Math]::Min(50, $commandLine.Length)))..." -ForegroundColor Green
        }
        # العمليات الآمنة للإغلاق
        elseif ($commandLine -match "nodemon|webpack|vite|dev-server|test") {
            $safeToKillNodeProcesses += $proc
            Write-Host "   ❌ سيتم إغلاق: $($proc.Id) - $($commandLine.Substring(0, [Math]::Min(50, $commandLine.Length)))..." -ForegroundColor Red
        }
        else {
            Write-Host "   ⚠️  غير محدد: $($proc.Id) - $($commandLine.Substring(0, [Math]::Min(50, $commandLine.Length)))..." -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "   ⚠️  لا يمكن قراءة: $($proc.Id)" -ForegroundColor Yellow
    }
}

# 2. فحص عمليات VS Code
Write-Host "`n🟡 فحص عمليات VS Code..." -ForegroundColor Yellow

$codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
$safeToKillCodeProcesses = @()

foreach ($proc in $codeProcesses) {
    # تجنب إغلاق VS Code الحالي
    if ($proc.Id -eq $currentVSCodePID -or $KeepVSCode) {
        Write-Host "   🔒 محمي: VS Code PID $($proc.Id)" -ForegroundColor Green
    }
    # العمليات الفرعية الآمنة للإغلاق
    elseif ($proc.WorkingSet -lt 100MB) {
        $safeToKillCodeProcesses += $proc
        Write-Host "   ❌ سيتم إغلاق: VS Code Helper PID $($proc.Id) ($(($proc.WorkingSet/1MB).ToString('F1')) MB)" -ForegroundColor Red
    }
    else {
        Write-Host "   ⚠️  كبير الحجم: VS Code PID $($proc.Id) ($(($proc.WorkingSet/1MB).ToString('F1')) MB)" -ForegroundColor Yellow
    }
}

# 3. فحص عمليات PowerShell الإضافية
Write-Host "`n🟡 فحص عمليات PowerShell..." -ForegroundColor Yellow

$psProcesses = Get-Process -Name "powershell" -ErrorAction SilentlyContinue | Where-Object { $_.Id -ne $PID }
foreach ($proc in $psProcesses) {
    Write-Host "   ❌ سيتم إغلاق: PowerShell PID $($proc.Id)" -ForegroundColor Red
}

# 4. فحص عمليات أخرى
Write-Host "`n🟡 فحص عمليات أخرى..." -ForegroundColor Yellow

$otherProcesses = @()
$processesToCheck = @("code-tunnel", "cloudcode_cli")

foreach ($processName in $processesToCheck) {
    $procs = Get-Process -Name $processName -ErrorAction SilentlyContinue
    foreach ($proc in $procs) {
        $otherProcesses += $proc
        Write-Host "   ❌ سيتم إغلاق: $processName PID $($proc.Id)" -ForegroundColor Red
    }
}

# عرض الملخص
Write-Host "`n📊 ملخص العمليات:" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan
Write-Host "🔒 Node.js محمي: $($criticalNodeProcesses.Count)" -ForegroundColor Green
Write-Host "❌ Node.js سيتم إغلاق: $($safeToKillNodeProcesses.Count)" -ForegroundColor Red
Write-Host "❌ VS Code Helpers سيتم إغلاق: $($safeToKillCodeProcesses.Count)" -ForegroundColor Red
Write-Host "❌ PowerShell سيتم إغلاق: $($psProcesses.Count)" -ForegroundColor Red
Write-Host "❌ أخرى سيتم إغلاق: $($otherProcesses.Count)" -ForegroundColor Red

$totalToKill = $safeToKillNodeProcesses.Count + $safeToKillCodeProcesses.Count + $psProcesses.Count + $otherProcesses.Count

if ($totalToKill -eq 0) {
    Write-Host "`n✅ لا توجد عمليات تحتاج إغلاق!" -ForegroundColor Green
    exit 0
}

# تأكيد الإغلاق
if (-not $DryRun -and -not $Force) {
    Write-Host "`n⚠️  هل تريد المتابعة؟ (y/N): " -ForegroundColor Yellow -NoNewline
    $confirmation = Read-Host
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-Host "❌ تم الإلغاء" -ForegroundColor Red
        exit 1
    }
}

if ($DryRun) {
    Write-Host "`n🔍 وضع المعاينة - لن يتم إغلاق أي عمليات" -ForegroundColor Yellow
    exit 0
}

# إغلاق العمليات
Write-Host "`n🚀 بدء إغلاق العمليات..." -ForegroundColor Yellow

$killedCount = 0

# إغلاق Node.js الآمنة
foreach ($proc in $safeToKillNodeProcesses) {
    try {
        Stop-Process -Id $proc.Id -Force
        Write-Host "   ✅ تم إغلاق Node.js PID $($proc.Id)" -ForegroundColor Green
        $killedCount++
    }
    catch {
        Write-Host "   ❌ فشل إغلاق Node.js PID $($proc.Id): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# إغلاق VS Code Helpers
foreach ($proc in $safeToKillCodeProcesses) {
    try {
        Stop-Process -Id $proc.Id -Force
        Write-Host "   ✅ تم إغلاق VS Code Helper PID $($proc.Id)" -ForegroundColor Green
        $killedCount++
    }
    catch {
        Write-Host "   ❌ فشل إغلاق VS Code Helper PID $($proc.Id): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# إغلاق PowerShell الإضافية
foreach ($proc in $psProcesses) {
    try {
        Stop-Process -Id $proc.Id -Force
        Write-Host "   ✅ تم إغلاق PowerShell PID $($proc.Id)" -ForegroundColor Green
        $killedCount++
    }
    catch {
        Write-Host "   ❌ فشل إغلاق PowerShell PID $($proc.Id): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# إغلاق العمليات الأخرى
foreach ($proc in $otherProcesses) {
    try {
        Stop-Process -Id $proc.Id -Force
        Write-Host "   ✅ تم إغلاق $($proc.ProcessName) PID $($proc.Id)" -ForegroundColor Green
        $killedCount++
    }
    catch {
        Write-Host "   ❌ فشل إغلاق $($proc.ProcessName) PID $($proc.Id): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🎉 تم الانتهاء!" -ForegroundColor Green
Write-Host "تم إغلاق $killedCount من أصل $totalToKill عملية" -ForegroundColor Cyan

# فحص الذاكرة بعد التنظيف
Write-Host "`n💾 استخدام الذاكرة بعد التنظيف:" -ForegroundColor Cyan
$memoryInfo = Get-WmiObject -Class Win32_OperatingSystem
$totalMemory = [math]::Round($memoryInfo.TotalVisibleMemorySize / 1MB, 2)
$freeMemory = [math]::Round($memoryInfo.FreePhysicalMemory / 1MB, 2)
$usedMemory = $totalMemory - $freeMemory
$memoryUsagePercent = [math]::Round(($usedMemory / $totalMemory) * 100, 1)

Write-Host "المستخدم: $usedMemory GB من $totalMemory GB ($memoryUsagePercent%)" -ForegroundColor White
