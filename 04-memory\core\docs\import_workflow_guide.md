# دليل استيراد AI Dev Assistant Workflow إلى n8n

## الطريقة السهلة: استيراد الملف مباشرة

### الخطوة 1: الوصول إلى n8n
1. افتح المتصفح واذهب إلى: `http://localhost:5678`
2. سجل الدخول باستخدام:
   - Email: `<EMAIL>`
   - Password: `2452329511@Amr`

### الخطوة 2: استيراد الـ Workflow
1. في الصفحة الرئيسية لـ n8n، انقر على **"Import from file"** أو **"استيراد من ملف"**
2. اختر الملف: `ai_dev_assistant_workflow.json`
3. انقر على **"Import"** أو **"استيراد"**

### الخطوة 3: إعداد Credentials

#### إعداد Google Gemini API:
1. اذهب إلى **Settings** → **Credentials**
2. انقر على **"Add Credential"**
3. اختر **"Google Gemini API"**
4. أدخل API Key الخاص بك
5. احفظ الـ credential

#### تحديث عقدة Google Gemini:
1. انقر على عقدة **"Google Gemini"** في الـ workflow
2. في قسم **Credentials**، اختر الـ credential الذي أنشأته
3. احفظ التغييرات

### الخطوة 4: التحقق من إعدادات Ollama
1. انقر على عقدة **"Ollama"**
2. تأكد من أن **Base URL** هو: `http://host.docker.internal:11434`
3. تأكد من أن **Model** هو: `llama3:8b`

### الخطوة 5: إنشاء مجلد الإخراج
قم بتشغيل هذا الأمر لإنشاء مجلد الإخراج:
```bash
docker exec n8n_automation mkdir -p /data/output
```

### الخطوة 6: تفعيل الـ Workflow
1. احفظ الـ workflow (Ctrl+S)
2. انقر على زر **"Active"** لتفعيل الـ workflow
3. ستحصل على webhook URL مثل:
   `http://localhost:5678/webhook/a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22`

## اختبار الـ Workflow

### استخدام curl:
```bash
curl -X POST http://localhost:5678/webhook/a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22 \
  -H "Content-Type: application/json" \
  -d '{
    "feature_request": "نظام تسجيل دخول للمستخدمين مع JWT",
    "feature_name": "user_auth_system"
  }'
```

### استخدام Python:
```python
import requests

url = "http://localhost:5678/webhook/a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22"
data = {
    "feature_request": "نظام إدارة المهام مع قاعدة بيانات",
    "feature_name": "task_manager"
}

response = requests.post(url, json=data)
print(response.status_code)
print(response.text)
```

## مراقبة النتائج
1. في n8n، اذهب إلى **"Executions"** لمراقبة تنفيذ الـ workflow
2. تحقق من مجلد `/data/output/` للملفات المُنشأة
3. يمكنك عرض الملفات باستخدام:
```bash
docker exec n8n_automation ls -la /data/output/
docker exec n8n_automation cat /data/output/your_file.md
```

## استكشاف الأخطاء الشائعة

### خطأ في Ollama:
- تأكد من أن Ollama يعمل محلياً
- تحقق من أن النموذج `llama3:8b` متاح
- جرب تغيير Base URL إلى `http://**********:11434`

### خطأ في Google Gemini:
- تأكد من صحة API Key
- تحقق من أن لديك رصيد في Google Cloud

### خطأ في كتابة الملف:
- تأكد من وجود مجلد `/data/output/`
- تحقق من أذونات الكتابة

## ملاحظات مهمة:
- الـ workflow يستخدم نموذجين AI: Gemini للتخطيط و Llama3 للكود
- النتيجة النهائية ستكون ملف Markdown يحتوي على الكود المُنشأ
- يمكنك تعديل الـ prompts حسب احتياجاتك
