# 📋 تحليل مشروع AnythingLLM

## 🔍 نظرة عامة

هذا المجلد يحتوي على مشروع **AnythingLLM** الكامل من Mintplex Labs - وهو تطبيق شامل للذكاء الاصطناعي يسمح بتحويل أي وثيقة أو محتوى إلى سياق يمكن لأي نموذج لغوي استخدامه.

## 📁 البنية الرئيسية

### المكونات الأساسية:
- **`frontend/`** - واجهة المستخدم (React + Vite)
- **`server/`** - خادم النود جي إس الرئيسي
- **`collector/`** - معالج الوثائق والملفات
- **`docker/`** - إعدادات Docker والنشر
- **`embed/`** - أداة التضمين في المواقع
- **`browser-extension/`** - إضافة المتصفح

### الميزات المتقدمة:
- ✅ دعم MCP (Model Context Protocol) الكامل
- ✅ بناء AI Agents بدون كود
- ✅ دعم متعدد الوسائط (صور + نص)
- ✅ عوامل ذكية مخصصة
- ✅ دعم متعدد المستخدمين مع الصلاحيات
- ✅ أداة دردشة قابلة للتضمين
- ✅ دعم أنواع متعددة من الوثائق

## 🤖 النماذج المدعومة

### نماذج اللغة الكبيرة:
- OpenAI، Azure OpenAI، AWS Bedrock
- Google Gemini، Anthropic Claude
- Ollama، LM Studio، LocalAI
- Hugging Face، Together AI، Fireworks
- Groq، Cohere، Mistral، وأكثر...

### نماذج التضمين:
- AnythingLLM Native Embedder
- OpenAI، Azure OpenAI
- LocalAI، Ollama، LM Studio
- Cohere

### قواعد البيانات المتجهة:
- LanceDB (افتراضي)
- PGVector، Pinecone، Chroma
- Weaviate، Qdrant، Milvus

## 🚀 كيفية التشغيل

### باستخدام Docker (الأسهل):
```bash
cd anything-llm/docker
docker-compose up -d
```

### للتطوير:
```bash
cd anything-llm
yarn setup
yarn dev:server
yarn dev:frontend
yarn dev:collector
```

## 🔧 الإعدادات المهمة

### ملفات البيئة:
- `server/.env.development` - إعدادات الخادم
- `collector/.env` - إعدادات معالج الوثائق
- `docker/.env` - إعدادات Docker

### الموانئ الافتراضية:
- Frontend: `3000`
- Server: `3001`
- Collector: `8888`

## 📚 الوثائق والموارد

- [الموقع الرسمي](https://anythingllm.com)
- [التوثيق](https://docs.anythingllm.com)
- [المستودع الأصلي](https://github.com/Mintplex-Labs/anything-llm)
- [Discord المجتمع](https://discord.gg/6UyHPeGZAC)

## 🛠️ التكامل مع مشروعنا

هذا المشروع يعمل كواحد من المكونات الأساسية في نظامنا المتكامل:
- **n8n** يدير سير العمل والأتمتة
- **Ollama** يشغل النماذج المحلية
- **AnythingLLM** يوفر واجهة إدارة المعرفة والمحادثات

## 🔐 الخصوصية والأمان

- يدعم التشغيل المحلي الكامل
- إعدادات خصوصية قابلة للتخصيص
- نظام صلاحيات متقدم للمؤسسات
- تشفير البيانات الحساسة

---

*تم تحليله في: ${new Date().toLocaleDateString('ar-EG')}*
