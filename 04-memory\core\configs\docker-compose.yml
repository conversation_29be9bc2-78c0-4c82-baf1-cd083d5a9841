services:
  # خدمة n8n - مدير العمليات والتنسيق
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n_orchestrator
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - NODE_ENV=production
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=Africa/Cairo
      # متغيرات Google Cloud
      - GOOGLE_CLOUD_PROJECT=ai-integrated-system-2025
      - GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/.gcp/service-account-key.json
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./service-account-key.json:/home/<USER>/.gcp/service-account-key.json:ro
    networks:
      - ai_network
    depends_on:
      - ollama
      - anything_llm

  # خدمة Ollama - النماذج المحلية  
  ollama:
    image: ollama/ollama:latest
    container_name: ollama_local_models
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
    networks:
      - ai_network
    # إضافة دعم GPU إذا كان متاح
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # خدمة AnythingLLM - مركز القيادة والذاكرة (صورة جاهزة)
  anything_llm:
    image: mintplexlabs/anythingllm:latest
    container_name: anything_llm_commander
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - STORAGE_DIR=/app/server/storage
      - UID=1000
      - GID=1000
      # ربط مع Ollama
      - OLLAMA_BASE_URL=http://ollama:11434
      # ربط مع Google Cloud
      - GOOGLE_CLOUD_PROJECT=ai-integrated-system-2025
      # إعدادات إضافية للصورة الجاهزة
      - SERVER_PORT=3001
      - JWT_SECRET=my-random-string-for-jwt
      - INNGEST_EVENT_KEY=background-service-key
      - DISABLE_TELEMETRY=true
    volumes:
      - anything_llm_data:/app/server/storage
      - anything_llm_hotdir:/app/collector/hotdir
      - anything_llm_outputs:/app/collector/outputs
    networks:
      - ai_network
    depends_on:
      - ollama
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 45s

volumes:
  n8n_data:
    driver: local
  ollama_data:
    driver: local
  anything_llm_data:
    driver: local
  anything_llm_hotdir:
    driver: local
  anything_llm_outputs:
    driver: local

networks:
  ai_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
