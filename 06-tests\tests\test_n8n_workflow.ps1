# 🧪 اختبار n8n Workflow المتكامل
# =====================================

Write-Host "🚀 اختبار n8n Workflow المتكامل..." -ForegroundColor Green

# اختبار 1: فحص حالة n8n
Write-Host "`n📊 فحص حالة n8n..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:4002/healthz" -Method GET -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ n8n: يعمل بنجاح" -ForegroundColor Green
    }
}
catch {
    Write-Host "❌ n8n: لا يعمل - $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 تأكد من تشغيل n8n: docker-compose up -d n8n" -ForegroundColor Yellow
    return
}

# اختبار 2: فحص حالة الخدمات المطلوبة
Write-Host "`n🔍 فحص الخدمات المطلوبة..." -ForegroundColor Yellow

$services = @(
    @{Name="AI Coordinator"; URL="http://localhost:4003/api/health"},
    @{Name="Ollama"; URL="http://localhost:11434/api/tags"},
    @{Name="AnythingLLM"; URL="http://localhost:4001/api/v1/system/health"}
)

$allServicesRunning = $true

foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.URL -Method GET -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name): يعمل" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "❌ $($service.Name): لا يعمل" -ForegroundColor Red
        $allServicesRunning = $false
    }
}

if (-not $allServicesRunning) {
    Write-Host "`n⚠️ بعض الخدمات لا تعمل. يرجى التأكد من تشغيل جميع الخدمات أولاً." -ForegroundColor Yellow
    return
}

# اختبار 3: اختبار workflow بسيط (complexity: medium)
Write-Host "`n🧪 اختبار workflow بسيط..." -ForegroundColor Yellow

$simpleTest = @{
    prompt = "مرحبا، هذا اختبار بسيط للنظام"
    complexity = "medium"
} | ConvertTo-Json

try {
    Write-Host "📤 إرسال طلب بسيط..." -ForegroundColor Cyan
    $response = Invoke-RestMethod -Uri "http://localhost:4002/webhook/ai-dev-assistant" -Method POST -ContentType "application/json" -Body $simpleTest -TimeoutSec 30
    
    Write-Host "✅ الاختبار البسيط نجح!" -ForegroundColor Green
    Write-Host "📝 النتيجة:" -ForegroundColor Cyan
    Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor White
}
catch {
    Write-Host "❌ فشل الاختبار البسيط: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 تحقق من أن workflow مستورد ومفعل في n8n" -ForegroundColor Yellow
}

# اختبار 4: اختبار workflow معقد (complexity: high)
Write-Host "`n🔬 اختبار workflow معقد..." -ForegroundColor Yellow

$complexTest = @{
    prompt = "اكتب دالة JavaScript لحساب المتوسط مع معالجة الأخطاء"
    complexity = "high"
} | ConvertTo-Json

try {
    Write-Host "📤 إرسال طلب معقد..." -ForegroundColor Cyan
    $response = Invoke-RestMethod -Uri "http://localhost:4002/webhook/ai-dev-assistant" -Method POST -ContentType "application/json" -Body $complexTest -TimeoutSec 60
    
    Write-Host "✅ الاختبار المعقد نجح!" -ForegroundColor Green
    Write-Host "📝 التحليل الاستراتيجي:" -ForegroundColor Cyan
    Write-Host $response.strategic_analysis -ForegroundColor White
    Write-Host "`n📝 نتيجة التنفيذ:" -ForegroundColor Cyan
    Write-Host $response.execution_result -ForegroundColor White
}
catch {
    Write-Host "❌ فشل الاختبار المعقد: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 تحقق من إعداد Gemini API credentials في n8n" -ForegroundColor Yellow
}

# اختبار 5: اختبار تكامل VS Code
Write-Host "`n💻 اختبار تكامل VS Code..." -ForegroundColor Yellow

try {
    Write-Host "📤 اختبار VS Code AI Helper..." -ForegroundColor Cyan
    $testResult = node vscode_ai_helper.js health 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ VS Code AI Helper يعمل" -ForegroundColor Green
    } else {
        Write-Host "⚠️ VS Code AI Helper يحتاج تحسين" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "❌ خطأ في VS Code AI Helper" -ForegroundColor Red
}

# اختبار 6: فحص Docker containers
Write-Host "`n🐳 فحص Docker containers..." -ForegroundColor Yellow

try {
    $containers = docker ps --filter "name=n8n" --filter "name=anythingllm" --filter "name=ai-coordinator" --format "table {{.Names}}\t{{.Status}}"
    Write-Host "📊 حالة Containers:" -ForegroundColor Cyan
    Write-Host $containers -ForegroundColor White
}
catch {
    Write-Host "❌ خطأ في فحص Docker containers" -ForegroundColor Red
}

# النتيجة النهائية
Write-Host "`n🎯 ملخص الاختبار:" -ForegroundColor Green
Write-Host "1. n8n: متاح ويعمل" -ForegroundColor White
Write-Host "2. الخدمات المساعدة: متاحة" -ForegroundColor White
Write-Host "3. Workflow البسيط: تم اختباره" -ForegroundColor White
Write-Host "4. Workflow المعقد: تم اختباره" -ForegroundColor White
Write-Host "5. VS Code Integration: متاح" -ForegroundColor White

Write-Host "`n📋 الخطوات التالية:" -ForegroundColor Green
Write-Host "1. افتح http://localhost:4002 لإدارة workflows" -ForegroundColor White
Write-Host "2. استورد ai_integrated_workflow.json إذا لم تفعل" -ForegroundColor White
Write-Host "3. أعد Gemini API credentials" -ForegroundColor White
Write-Host "4. فعل workflow" -ForegroundColor White
Write-Host "5. جرب الاختبارات من VS Code" -ForegroundColor White

Write-Host "`n🎉 انتهى اختبار n8n Workflow!" -ForegroundColor Green
