# 🚀 دليل إعداد AnythingLLM

## 📋 خطوات الإعداد الأولي

### 1. الوصول إلى AnythingLLM
- افتح المتصفح واذهب إلى: http://localhost:4001
- ستظهر لك شاشة الإعداد الأولي

### 2. إعداد LLM Provider (Ollama)
1. في شاشة الإعداد، اختر **"LLM Provider"**
2. اختر **"Ollama"** من القائمة
3. أدخل الإعدادات التالية:
   - **Base URL**: `http://host.docker.internal:11434`
   - **Model**: `llama3:8b` (أو أي نموذج آخر متاح)

### 3. إعداد Embedding Provider
1. اختر **"Embedding Provider"**
2. اختر **"Ollama"** 
3. أدخل الإعدادات:
   - **Base URL**: `http://host.docker.internal:11434`
   - **Model**: `llama3:8b`

### 4. إعداد Vector Database
1. اختر **"Vector Database"**
2. اختر **"LanceDB"** (الافتراضي)
3. اتركه كما هو

### 5. إنهاء الإعداد
1. انقر على **"Continue"** أو **"Finish Setup"**
2. ستنتقل إلى الواجهة الرئيسية

## 🧪 اختبار الاتصال

### اختبار بسيط:
1. في الواجهة الرئيسية، اكتب رسالة بسيطة مثل: "مرحبا، كيف حالك؟"
2. اضغط Enter أو انقر على إرسال
3. يجب أن تحصل على رد من النموذج

### اختبار متقدم:
1. اطلب من النموذج كتابة كود بسيط
2. اسأله عن معلومات تقنية
3. جرب محادثة طويلة لاختبار الذاكرة

## 🔧 إعدادات متقدمة

### إضافة مستندات:
1. انقر على **"Workspaces"** أو **"Documents"**
2. ارفع ملفات PDF أو نصوص
3. انتظر حتى يتم معالجتها
4. ابدأ محادثة حول محتوى المستندات

### إنشاء Workspace جديد:
1. انقر على **"New Workspace"**
2. أدخل اسم المشروع
3. اختر النماذج المناسبة
4. ابدأ العمل

## 🎯 اختبارات مقترحة

### الاختبار 1: محادثة بسيطة
```
المستخدم: مرحبا، ما اسمك؟
النموذج: [يجب أن يرد بشكل طبيعي]
```

### الاختبار 2: كتابة كود
```
المستخدم: اكتب دالة JavaScript لحساب مجموع رقمين
النموذج: [يجب أن يكتب كود صحيح]
```

### الاختبار 3: تحليل نص
```
المستخدم: لخص لي هذا النص: [نص طويل]
النموذج: [يجب أن يقدم ملخص مفيد]
```

## ❌ استكشاف الأخطاء

### مشكلة: لا يتصل بـ Ollama
- تأكد من أن Ollama يعمل: `ollama list`
- تحقق من URL: `http://host.docker.internal:11434`
- أعد تشغيل AnythingLLM: `docker-compose restart anythingllm`

### مشكلة: النموذج لا يرد
- تحقق من أن النموذج متاح في Ollama
- جرب نموذج آخر مثل `mistral:7b`
- تحقق من logs: `docker-compose logs anythingllm`

### مشكلة: بطء في الاستجابة
- هذا طبيعي للنماذج الكبيرة
- جرب نموذج أصغر مثل `phi3:mini`
- تأكد من أن الجهاز لديه ذاكرة كافية

## 🎉 النجاح!

إذا تمكنت من:
- ✅ الدخول إلى AnythingLLM
- ✅ ربطه بـ Ollama
- ✅ الحصول على ردود من النماذج
- ✅ رفع مستندات (اختياري)

فأنت جاهز للانتقال للخطوة التالية!

---

**💡 نصيحة**: احفظ إعداداتك واختبر نماذج مختلفة لتجد الأنسب لاحتياجاتك.
