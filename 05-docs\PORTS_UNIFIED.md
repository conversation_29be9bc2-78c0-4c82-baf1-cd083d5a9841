# 🔌 دليل المنافذ الموحدة - AI Development Assistant

## 📊 المنافذ المنظمة

| الخدمة | المنفذ | URL الوصول | الوصف |
|--------|-------|------------|-------|
| **AI Coordinator** | `3333` | http://localhost:3333 | 🧠 طبقة التنسيق الذكي |
| **AnythingLLM** | `3001` | http://localhost:3001 | 💬 نظام إدارة المعرفة |
| **Ollama WebUI** | `3000` | http://localhost:3000 | 🖥️ واجهة Ollama |
| **n8n** | `5678` | http://localhost:5678 | 🔄 أتمتة سير العمل |
| **Ollama API** | `11434` | http://localhost:11434 | 🤖 خادم النماذج المحلية |

## ✅ النماذج المتاحة

بما أن جميع النماذج موجودة على التطبيق، يمكنك الآن:

1. **🧠 AI Coordinator**: http://localhost:3333 - **المشروع الرئيسي**
   - تنسيق ذكي بين جميع النماذج
   - API موحد للوصول لجميع الخدمات
   - منطق اتخاذ قرار ذكي

2. **💬 AnythingLLM**: http://localhost:3001 - واجهة المحادثات
3. **🖥️ Ollama WebUI**: http://localhost:3000 - إدارة النماذج المحلية
4. **🔄 n8n**: http://localhost:5678 - أتمتة سير العمل
5. **🤖 Ollama API**: http://localhost:11434 - الوصول المباشر للنماذج

## 🎯 فوائد التوحيد

- **سهولة الإدارة**: ملف docker-compose واحد
- **شبكة موحدة**: `ai-dev-network`
- **تشغيل مبسط**: أمر واحد لكل شيء
- **مراقبة مركزية**: فحص جميع الخدمات

## 🚀 كيفية الاستخدام

### تشغيل النظام:
```bash
# Linux/Mac
./start-unified.sh

# Windows PowerShell
.\start-unified.ps1

# أو مباشرة
docker-compose up -d
```

### إيقاف النظام:
```bash
docker-compose down
```

### فحص الحالة:
```bash
docker-compose ps
```

### عرض السجلات:
```bash
# جميع الخدمات
docker-compose logs -f

# خدمة محددة
docker-compose logs -f anythingllm
docker-compose logs -f ollama
docker-compose logs -f n8n
docker-compose logs -f ollama-webui
```

## 🔐 بيانات الاعتماد الافتراضية

### n8n:
- **المستخدم**: `admin`
- **كلمة المرور**: `password123`
- **URL**: http://localhost:5678

### AnythingLLM:
- **التوكن**: محدد في ملف `.env`
- **URL**: http://localhost:3001

### Ollama WebUI:
- **مفتاح سري**: محدد في ملف `.env`
- **URL**: http://localhost:3000

## 🔄 تدفق العمل المتكامل

```mermaid
graph TD
    A[User Request] --> B[n8n Webhook :5678]
    B --> C[Google Gemini API]
    C --> D[Ollama :11434]
    D --> E[AnythingLLM :3001]
    E --> F[Ollama WebUI :3000]
    
    G[Docker Network: ai-dev-network] --> A
    G --> B
    G --> D
    G --> E
    G --> F
```

## 📁 هيكل الملفات

```
ai-development-assistant/
├── docker-compose.yml          # ملف التكوين الموحد
├── .env                        # متغيرات البيئة
├── start-unified.sh           # سكريبت تشغيل Linux/Mac
├── start-unified.ps1          # سكريبت تشغيل Windows
├── PORTS_UNIFIED.md           # هذا الملف
└── volumes/                   # مجلدات التخزين الدائم
    ├── anythingllm_storage/
    ├── n8n_data/
    ├── ollama_data/
    └── ollama_webui_data/
```

## 🛠️ استكشاف الأخطاء

### مشكلة: خدمة لا تستجيب
```bash
# فحص حالة الحاوية
docker-compose ps

# فحص السجلات
docker-compose logs [service_name]

# إعادة تشغيل خدمة محددة
docker-compose restart [service_name]
```

### مشكلة: منفذ مشغول
```bash
# فحص المنافذ المستخدمة
netstat -tulpn | grep :3001
netstat -tulpn | grep :5678

# إيقاف العمليات المتضاربة
sudo kill -9 [PID]
```

### مشكلة: مساحة القرص
```bash
# تنظيف Docker
docker system prune -a

# حذف الحاويات المتوقفة
docker container prune

# حذف الصور غير المستخدمة
docker image prune -a
```

## 🔧 التخصيص

### تغيير المنافذ:
1. عدل ملف `docker-compose.yml`
2. غير قيم `ports` للخدمة المطلوبة
3. حدث ملف `.env` إذا لزم الأمر
4. أعد تشغيل النظام

### إضافة خدمة جديدة:
1. أضف الخدمة في `docker-compose.yml`
2. أضف المتغيرات في `.env`
3. حدث سكريبتات التشغيل
4. حدث هذا الملف

## 📈 مراقبة الأداء

### فحص استخدام الموارد:
```bash
# استخدام CPU والذاكرة
docker stats

# مساحة القرص
docker system df

# معلومات الشبكة
docker network ls
docker network inspect ai-dev-network
```

## 🔒 الأمان

### نصائح الأمان:
- ✅ غير كلمات المرور الافتراضية
- ✅ استخدم HTTPS في الإنتاج
- ✅ قم بتحديث الصور بانتظام
- ✅ راقب السجلات للأنشطة المشبوهة
- ✅ استخدم جدار حماية

### متغيرات البيئة الحساسة:
```bash
# في ملف .env
AUTH_TOKEN=your-secure-token
JWT_SECRET=your-jwt-secret
N8N_ENCRYPTION_KEY=your-encryption-key
WEBUI_SECRET_KEY=your-webui-secret
```

## 📞 الدعم والمساعدة

### للمساعدة:
1. راجع السجلات أولاً
2. تحقق من حالة الخدمات
3. راجع هذا الدليل
4. ابحث في الوثائق الرسمية

### روابط مفيدة:
- [AnythingLLM Docs](https://docs.anythingllm.com/)
- [n8n Documentation](https://docs.n8n.io/)
- [Ollama Documentation](https://ollama.ai/docs)
- [Docker Compose Reference](https://docs.docker.com/compose/)

---
**آخر تحديث**: 2025-07-05  
**الإصدار**: 1.0  
**المطور**: AI Development Assistant Team
