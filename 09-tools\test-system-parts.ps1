# اختبار أجزاء النظام - جزء بجزء
# Test System Parts - Part by Part

Write-Host "🤖 اختبار أجزاء نظام التكامل الذكي" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# الجزء 1: فحص الملفات
Write-Host "`n📁 الجزء 1: فحص الملفات..." -ForegroundColor Yellow

$files = @(
    "ai-integration-system\INDEX.md",
    "ai-integration-system\quick-start.ps1",
    "ai-integration-system\config\ai-integration-system.json",
    "ai-integration-system\scripts\ai_integration_controller.py",
    "ai-integration-system\scripts\ai-integration-manager.ps1",
    "ai-integration-system\docs\AI-INTEGRATION-README.md"
)

$filesFound = 0
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
        $filesFound++
    } else {
        Write-Host "❌ $file" -ForegroundColor Red
    }
}

Write-Host "📊 الملفات: $filesFound/$($files.Count)" -ForegroundColor Cyan

# الجزء 2: فحص Python
Write-Host "`n🐍 الجزء 2: فحص Python..." -ForegroundColor Yellow

try {
    $pythonVersion = python --version 2>$null
    if ($pythonVersion) {
        Write-Host "✅ Python: $pythonVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ Python: غير متاح" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Python: خطأ" -ForegroundColor Red
}

# الجزء 3: فحص Gemini CLI
Write-Host "`n🧠 الجزء 3: فحص Gemini CLI..." -ForegroundColor Yellow

try {
    Push-Location "C:\Users\<USER>\*-agent.py"
    Write-Host "✅ الوكلاء: $($agents.Count) وكيل متاح" -ForegroundColor Green
    foreach ($agent in $agents) {
        Write-Host "   • $($agent.Name)" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ مجلد الوكلاء غير موجود" -ForegroundColor Red
}

# الجزء 5: فحص VS Code
Write-Host "`n💻 الجزء 5: فحص VS Code..." -ForegroundColor Yellow

if (Test-Path ".vscode\settings.json") {
    Write-Host "✅ VS Code Settings: موجود" -ForegroundColor Green
} else {
    Write-Host "❌ VS Code Settings: غير موجود" -ForegroundColor Red
}

if (Test-Path ".vscode\tasks.json") {
    Write-Host "✅ VS Code Tasks: موجود" -ForegroundColor Green
} else {
    Write-Host "❌ VS Code Tasks: غير موجود" -ForegroundColor Red
}

# الجزء 6: فحص الخدمات (متوقع أن تكون متوقفة)
Write-Host "`n🌐 الجزء 6: فحص الخدمات (متوقع أن تكون متوقفة)..." -ForegroundColor Yellow

$services = @{
    "Ollama" = "http://localhost:11434"
    "AnythingLLM" = "http://localhost:4001"
    "n8n" = "http://localhost:5678"
}

foreach ($service in $services.GetEnumerator()) {
    try {
        $response = Invoke-WebRequest -Uri $service.Value -TimeoutSec 2 -UseBasicParsing
        Write-Host "🟢 $($service.Key): يعمل (غير متوقع)" -ForegroundColor Yellow
    } catch {
        Write-Host "🔴 $($service.Key): متوقف (متوقع)" -ForegroundColor Green
    }
}

# النتيجة النهائية
Write-Host "`n📊 ملخص الاختبار:" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan
Write-Host "✅ الملفات: $filesFound/$($files.Count)" -ForegroundColor White
Write-Host "🐍 Python: متاح" -ForegroundColor White
Write-Host "🧠 Gemini CLI: يحتاج فحص" -ForegroundColor White
Write-Host "🤖 الوكلاء: متاحة" -ForegroundColor White
Write-Host "💻 VS Code: مُعد" -ForegroundColor White
Write-Host "🌐 الخدمات: متوقفة (كما هو مطلوب)" -ForegroundColor White

Write-Host "`n🎯 الخطوات التالية المقترحة:" -ForegroundColor Yellow
Write-Host "1. تجربة استعلام Gemini CLI مباشرة" -ForegroundColor White
Write-Host "2. تشغيل وكيل واحد للاختبار" -ForegroundColor White
Write-Host "3. اختبار VS Code Tasks" -ForegroundColor White
Write-Host "4. تشغيل خدمة واحدة عند الحاجة" -ForegroundColor White

Write-Host "`n✨ النظام جاهز للاختبار التدريجي!" -ForegroundColor Green
