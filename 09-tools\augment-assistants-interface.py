#!/usr/bin/env python3
"""
Augment Assistants Interface
واجهة Augment للتواصل مع المساعدين
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from memory.memory_bridge import MemoryBridge

class AugmentAssistantsInterface:
    """واجهة Augment للتواصل مع المساعدين"""
    
    def __init__(self):
        self.memory = MemoryBridge()
        self.workspace_root = Path.cwd()
        
    def consult_gemini(self, query: str) -> str:
        """استشارة Gemini CLI"""
        try:
            # تسجيل في الذاكرة
            self.memory.sync_with_augment(f"استشارة Gemini CLI", f"السؤال: {query[:100]}...")
            
            # تغيير المجلد وتشغيل الاستعلام
            original_cwd = os.getcwd()
            os.chdir("C:/Users/<USER>")
            
            result = subprocess.run(
                ["gemini", query],
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8'
            )
            
            os.chdir(original_cwd)
            
            if result.returncode == 0:
                response = result.stdout.strip()
                # تسجيل النتيجة في الذاكرة
                self.memory.create_assistant_query_log("gemini_cli", query, response)
                self.memory.update_assistant_status("gemini_cli", "متاح", "آخر استعلام نجح")
                return response
            else:
                error_msg = f"خطأ: {result.stderr}"
                self.memory.update_assistant_status("gemini_cli", "خطأ", error_msg)
                return error_msg
                
        except subprocess.TimeoutExpired:
            error_msg = "خطأ: انتهت مهلة الاستعلام"
            self.memory.update_assistant_status("gemini_cli", "خطأ", error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"خطأ: {str(e)}"
            self.memory.update_assistant_status("gemini_cli", "خطأ", error_msg)
            return error_msg
    
    def delegate_to_agent(self, agent_name: str, task: str = "") -> str:
        """تفويض مهمة لوكيل"""
        try:
            # تسجيل في الذاكرة
            self.memory.sync_with_augment(f"تفويض مهمة للوكيل {agent_name}", task)
            
            agent_file = f"ai-agents/{agent_name}.py"
            if not Path(agent_file).exists():
                error_msg = f"الوكيل {agent_name} غير موجود"
                self.memory.update_assistant_status(agent_name, "غير موجود", error_msg)
                return error_msg
            
            result = subprocess.run(
                ["python", agent_file],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                response = result.stdout.strip()
                self.memory.create_assistant_query_log(agent_name, task, response)
                self.memory.update_assistant_status(agent_name, "نجح", "آخر مهمة نجحت")
                return response
            else:
                error_msg = f"خطأ: {result.stderr}"
                self.memory.update_assistant_status(agent_name, "خطأ", error_msg)
                return error_msg
                
        except Exception as e:
            error_msg = f"خطأ: {str(e)}"
            self.memory.update_assistant_status(agent_name, "خطأ", error_msg)
            return error_msg
    
    def get_project_status(self) -> str:
        """الحصول على حالة المشروع من الذاكرة"""
        return self.memory.generate_memory_report()
    
    def add_project_insight(self, category: str, insight: str):
        """إضافة رؤية جديدة للمشروع"""
        timestamp = self.memory.get_current_session_id()
        self.memory.add_project_knowledge(category, f"insight_{timestamp}", insight)
        self.memory.sync_with_augment("إضافة رؤية جديدة", f"{category}: {insight[:100]}...")
    
    def get_assistant_recommendations(self) -> str:
        """الحصول على توصيات لاستخدام المساعدين"""
        recommendations = self.memory.get_assistant_recommendations()
        
        if not recommendations:
            return "لا توجد توصيات حالياً. جميع المشاكل تحت السيطرة! ✅"
        
        result = ["💡 توصيات لاستخدام المساعدين:"]
        for issue, recommendation in recommendations.items():
            result.append(f"• {recommendation}")
        
        return "\n".join(result)
    
    def collaborative_analysis(self, topic: str) -> str:
        """تحليل تعاوني مع المساعدين"""
        results = []
        results.append(f"🔍 تحليل تعاوني: {topic}")
        results.append("=" * 50)
        
        # 1. استشارة Gemini أولاً
        results.append("\n🧠 استشارة Gemini CLI:")
        gemini_response = self.consult_gemini(f"حلل هذا الموضوع في مشروع AI Development Assistant: {topic}")
        results.append(f"📝 {gemini_response[:300]}...")
        
        # 2. محاولة استخدام File Search Agent
        results.append("\n🔍 محاولة استخدام File Search Agent:")
        file_search_response = self.delegate_to_agent("file-search-agent", f"البحث عن: {topic}")
        results.append(f"📝 {file_search_response[:200]}...")
        
        # 3. تسجيل النتائج في الذاكرة
        analysis_summary = f"تحليل تعاوني لـ {topic} - Gemini: متاح، Agents: يحتاج إصلاح"
        self.add_project_insight("collaborative_analysis", analysis_summary)
        
        results.append(f"\n💾 تم حفظ التحليل في الذاكرة المشتركة")
        
        return "\n".join(results)

# واجهة سهلة للاستخدام
def quick_consult(query: str) -> str:
    """استشارة سريعة مع Gemini"""
    interface = AugmentAssistantsInterface()
    return interface.consult_gemini(query)

def quick_delegate(agent: str, task: str = "") -> str:
    """تفويض سريع لوكيل"""
    interface = AugmentAssistantsInterface()
    return interface.delegate_to_agent(agent, task)

def quick_status() -> str:
    """حالة سريعة للمشروع"""
    interface = AugmentAssistantsInterface()
    return interface.get_project_status()

def main():
    """اختبار الواجهة"""
    interface = AugmentAssistantsInterface()
    
    print("🤖 واجهة Augment للمساعدين")
    print("=" * 40)
    
    # اختبار استشارة Gemini
    print("\n🧠 اختبار استشارة Gemini...")
    response = interface.consult_gemini("ما أفضل طريقة لتنظيم مشروع Python؟")
    print(f"📝 الاستجابة: {response[:200]}...")
    
    # اختبار حالة المشروع
    print("\n📊 حالة المشروع:")
    status = interface.get_project_status()
    print(status)
    
    # اختبار التوصيات
    print("\n💡 التوصيات:")
    recommendations = interface.get_assistant_recommendations()
    print(recommendations)
    
    print("\n✅ تم اختبار الواجهة بنجاح!")

if __name__ == "__main__":
    main()
